using CrateApi.Common.Dto.Response;
using CrateApi.Data;
using CrateApi.Data.Models;
using CrateApi.Services.Authentication;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Common;
using LanguageExt.Traits;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Database;

public static class TrackApprovalService<M, RT>
    where RT :
        Has<M, CrateDbContext>,
        Has<M, IUserManager>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    private static readonly Error InvalidValue = Expected.New(400, "Invalid value");
    private static readonly Error TrackNotFound = Expected.New(404, "Track not found");
    private static readonly Error UnauthorizedError = Expected.New(403, "Unauthorized - Admin access required");

    public static K<M, List<TrackResponseDto>> GetPendingTracks(int start, int size) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(GetPendingTracks), OL.Start)
        from _0            in guard(start >= 0 && size >= 0, InvalidValue)
        from _1            in guard(um.Current.IsAdmin, UnauthorizedError) // You'll need to add IsAdmin to IUserManager
        from tracks        in IO.liftAsync(() =>
            context.Tracks
                .Where(t => t.ApprovalStatus == ApprovalStatus.Pending)
                .OrderByDescending(t => t.Created)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        from _2            in _l.LogInformationIO("Fetched pending tracks from db, count: {Count}", tracks.Count)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(tracks))
        from endlog        in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(GetPendingTracks), OL.End)
        select mapped;

    public static K<M, TrackResponseDto> ApproveTrack(int trackId) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(ApproveTrack), OL.Start)
        from _0            in guard(trackId > 0, InvalidValue)
        from _1            in guard(um.Current.IsAdmin, UnauthorizedError)
        from track         in IO.liftAsync(() => context.Tracks.FirstOrDefaultAsync(t => t.Id == trackId))
        from _2            in guard(track is not null, TrackNotFound)
        from _3            in IO.lift(() => {
            track!.ApprovalStatus = ApprovalStatus.Approved;
            track.ApprovedBy = um.Current.UserId;
            track.ApprovedAt = DateTime.UtcNow;
            track.Updated = DateTime.UtcNow;
        })
        from updatedTrack  in context.UpdateAndSaveIO(track!)
        from _4            in _l.LogInformationIO("Approved track with id: {Id}", trackId)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(updatedTrack))
        from endlog        in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(ApproveTrack), OL.End)
        select mapped;

    public static K<M, TrackResponseDto> RejectTrack(int trackId) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(RejectTrack), OL.Start)
        from _0            in guard(trackId > 0, InvalidValue)
        from _1            in guard(um.Current.IsAdmin, UnauthorizedError)
        from track         in IO.liftAsync(() => context.Tracks.FirstOrDefaultAsync(t => t.Id == trackId))
        from _2            in guard(track is not null, TrackNotFound)
        from _3            in IO.lift(() => {
            track!.ApprovalStatus = ApprovalStatus.Rejected;
            track.ApprovedBy = um.Current.UserId;
            track.ApprovedAt = DateTime.UtcNow;
            track.Updated = DateTime.UtcNow;
        })
        from updatedTrack  in context.UpdateAndSaveIO(track!)
        from _4            in _l.LogInformationIO("Rejected track with id: {Id}", trackId)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(updatedTrack))
        from endlog        in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(RejectTrack), OL.End)
        select mapped;

    public static K<M, List<TrackResponseDto>> GetApprovedTracks(int start, int size) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(GetApprovedTracks), OL.Start)
        from _0            in guard(start >= 0 && size >= 0, InvalidValue)
        from _1            in guard(um.Current.IsAdmin, UnauthorizedError)
        from tracks        in IO.liftAsync(() =>
            context.Tracks
                .Where(t => t.ApprovalStatus == ApprovalStatus.Approved)
                .OrderByDescending(t => t.ApprovedAt)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        from _2            in _l.LogInformationIO("Fetched approved tracks from db, count: {Count}", tracks.Count)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(tracks))
        from endlog        in _l.LogMethod(nameof(TrackApprovalService<M, RT>), nameof(GetApprovedTracks), OL.End)
        select mapped;
}
