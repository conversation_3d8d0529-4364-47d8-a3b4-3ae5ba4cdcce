using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data;
using CrateApi.Data.Context;
using CrateApi.Data.Models;
using CrateApi.Data.Models.Interfaces;
using CrateApi.Services.Authentication;
using CrateApi.Services.Logic;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Common;
using LanguageExt.Traits;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Database;

public static class TrackService<M, RT>
    where RT :
        Has<M, CrateDbContext>,
        Has<M, IUserManager>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    private static readonly Error InvalidValue = Expected.New(400, $"Invalid value");

    private const string AddedTrack         = "User Added Track with Id: {0}";
    private const string DeletedTrack       = "User Deleted Track with Id: {0}";
    private const string DeletedAllTracks   = "User Deleted All of their Tracks";
    private static K<M, List<Track>> FindTrendingTracks(int start, int size) =>
        from context in Has<M, RT, CrateDbContext>.ask
        from tracks in IO.liftAsync(() =>
            context.Tracks
                .Where(t => t.ApprovalStatus == ApprovalStatus.Approved)
                .OrderByDescending(t => t.Updated)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        select tracks;
    private static K<M, List<Track>> FindRecentTracks(int start, int size) =>
        from context in Has<M, RT, CrateDbContext>.ask
        from um      in Has<M, RT, IUserManager>.ask
        from tracks  in IO.liftAsync(() =>
            context.Tracks
                .Where(t => t.UserId == um.Current.UserId)
                .OrderByDescending(t => t.Updated)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        select tracks;

    public static K<M, List<TrackResponseDto>> GetTrending(int start, int size) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackService<M, RT>), nameof(GetTrending), OL.Start)
        from _0            in guard(start >= 0 && size >= 0, InvalidValue)
        from tracks        in FindTrendingTracks(start, size)
        from _1            in _l.LogInformationIO("Fetched trending tracks from db, count: {Count}", tracks.Count)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(tracks))
        from endlog        in _l.LogMethod(nameof(TrackService<M, RT>), nameof(GetTrending), OL.End)
        select mapped;

    public static K<M, List<TrackResponseDto>> GetRecentTracks(int start, int size) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackService<M, RT>), nameof(GetRecentTracks), OL.Start)
        from _0            in guard(start >= 0 && size >= 0, InvalidValue)
        from tracks        in FindRecentTracks(start, size)
        from _1            in _l.LogInformationIO("Fetched recent tracks from db, count: {Count}", tracks.Count)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(tracks))
        from endlog        in _l.LogMethod(nameof(TrackService<M, RT>), nameof(GetRecentTracks), OL.End)
        select mapped;

    public static K<M, TrackResponseDto> Add(TrackRequestDto dtoTrack) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackService<M, RT>), nameof(Add), OL.Start)
        from track         in M.Pure(TrackMapper.Instance.ToEntity(dtoTrack))
        from _0            in DbTraits<M, RT>.AssignDbTraits(track)
        from addedTrack    in context.AddAndSaveIO(track)
        from _3            in _l.LogInformationIO("Successfully added track to db {Id}", addedTrack.Id!.Value)
        from mapped        in M.Pure(TrackMapper.Instance.FromEntity(track))
        from _5            in AuditService<M, RT>.CreateAudit(string.Format(AddedTrack, addedTrack.Id!))
        from endlog        in _l.LogMethod(nameof(TrackService<M, RT>), nameof(Add), OL.End)
        select mapped;

    public static K<M, Unit> DeleteAll() =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackService<M, RT>), nameof(DeleteAll), OL.Start)
        from tracks        in IO.lift(() => context.Tracks.Where(e => e.UserId == um.Current.UserId))
        from _0            in context.RemoveRangeAndSaveIO(tracks)
        from _1            in _l.LogInformationIO("Successfully deleted all tracks")
        from _5            in AuditService<M, RT>.CreateAudit(DeletedAllTracks)
        from endlog        in _l.LogMethod(nameof(TrackService<M, RT>), nameof(DeleteAll), OL.End)
        select unit;

    public static K<M, Unit> Delete(int id) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(TrackService<M, RT>), nameof(Delete), OL.Start)
        from _0            in guard(id >= 0, InvalidValue)
        from _t            in IO.liftAsync(() => context.Tracks.FirstOrDefaultAsync(e => e.UserId == um.Current.UserId && e.Id == id))
        from _1            in guard(_t is not null, Expected.New(204, $"Track with {id} does not exist. It may already be deleted."))
        from _2            in context.RemoveAndSaveIO<Track>(_t)
        from _3            in _l.LogInformationIO("Successfully deleted track with id: {Id}", id)
        from _5            in AuditService<M, RT>.CreateAudit(string.Format(DeletedTrack, _t.Id))
        from endlog        in _l.LogMethod(nameof(TrackService<M, RT>), nameof(Delete), OL.End)
        select unit;
}
