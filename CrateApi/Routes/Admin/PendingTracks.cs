using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Infrastructure;
using CrateApi.Models;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using static LanguageExt.Prelude;
using TrackApprovals = CrateApi.Services.Database.TrackApprovalService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;

namespace CrateApi.Routes.AdminEndpoints;

public sealed class PendingTracks(ApiRuntime runtime) : Endpoint<PaginationQuery>
{
    public override void Configure()
    {
        Get("admin/tracks/pending");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("Admin")
            .Produces<List<TrackResponseDto>>(200)
            .WithSummary("Get Pending Tracks")
            .WithDescription("""
                Returns tracks awaiting approval for trending display. Admin access required.
             """
        ));
    }

    public override async Task HandleAsync(PaginationQuery req, CancellationToken ct)
    {
        var result = await TrackApprovals.GetPendingTracks(req.Start, req.Size).RunAsync(runtime);
        await result.Match(
            succ  => SendOkAsync(succ),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
