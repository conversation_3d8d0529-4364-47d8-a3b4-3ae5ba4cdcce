using CrateApi.Common.Dto.Response;
using CrateApi.Infrastructure;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using static LanguageExt.Prelude;
using TrackApprovals = CrateApi.Services.Database.TrackApprovalService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;

namespace CrateApi.Routes.AdminEndpoints;

public sealed class RejectTrack(ApiRuntime runtime) : EndpointWithoutRequest
{
    public override void Configure()
    {
        Post("admin/tracks/{id}/reject");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("Admin")
            .Produces<TrackResponseDto>(200)
            .WithSummary("Reject Track")
            .WithDescription("""
                Reject a track from trending display. Admin access required.
             """
        ));
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var trackId = Route<int>("id");
        var result = await TrackApprovals.RejectTrack(trackId).RunAsync(runtime);
        await result.Match(
            succ  => SendOkAsync(succ),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
